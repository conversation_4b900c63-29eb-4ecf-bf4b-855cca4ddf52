//+------------------------------------------------------------------+
//|                                             SignalManager.mqh |
//|                        Copyright 2024, TrendRider Development |
//|                                             https://mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TrendRider Development"
#property link      "https://mql5.com"
#property version   "2.00"

#include "MarketAnalyzer.mqh"

//+------------------------------------------------------------------+
//| 交易信号枚举                                                      |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_TYPE
{
   SIGNAL_NONE = 0,        // 无信号
   SIGNAL_BUY = 1,         // 买入信号
   SIGNAL_SELL = -1        // 卖出信号
};

//+------------------------------------------------------------------+
//| 信号强度枚举                                                      |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_STRENGTH
{
   SIGNAL_WEAK = 1,        // 弱信号
   SIGNAL_MEDIUM = 2,      // 中等信号
   SIGNAL_STRONG = 3       // 强信号
};

//+------------------------------------------------------------------+
//| 交易信号结构体                                                     |
//+------------------------------------------------------------------+
struct TradingSignal
{
   ENUM_SIGNAL_TYPE       signal_type;        // 信号类型
   ENUM_SIGNAL_STRENGTH   strength;           // 信号强度
   double                 entry_price;        // 建议入场价
   double                 stop_loss;          // 建议止损价
   double                 take_profit;        // 建议止盈价
   double                 confidence;         // 信号置信度 (0-1)
   datetime               signal_time;        // 信号生成时间
   string                 description;        // 信号描述
   N_Structure            structure;          // 相关的N字结构
   bool                   is_valid;          // 信号是否有效
   
   // 构造函数
   TradingSignal()
   {
      signal_type = SIGNAL_NONE;
      strength = SIGNAL_WEAK;
      entry_price = 0.0;
      stop_loss = 0.0;
      take_profit = 0.0;
      confidence = 0.0;
      signal_time = 0;
      description = "";
      is_valid = false;
   }
};

//+------------------------------------------------------------------+
//| 交易信号管理器类                                                   |
//+------------------------------------------------------------------+
class CSignalManager
{
private:
   // 成员变量
   string              m_symbol;           // 交易品种
   CMarketAnalyzer*    m_analyzer;         // 市场分析器
   int                 m_atr_handle;       // ATR指标句柄
   
   // 私有方法
   bool InitATRIndicator();
   double GetATR(int period, int shift = 0);
   bool CheckBullishEngulfing(ENUM_TIMEFRAMES timeframe, int shift = 1);
   bool CheckBearishEngulfing(ENUM_TIMEFRAMES timeframe, int shift = 1);
   double CalculateStopLoss(const N_Structure &structure, ENUM_SIGNAL_TYPE signal_type);
   double CalculateTakeProfit(double entry_price, double stop_loss, double risk_reward_ratio = 2.0);
   ENUM_SIGNAL_STRENGTH DetermineSignalStrength(const TradingSignal &signal);
   
public:
   // 构造函数
   CSignalManager(string symbol, CMarketAnalyzer* analyzer);
   
   // 析构函数
   ~CSignalManager();
   
   // 初始化信号管理器
   bool Initialize();
   
   // 生成交易信号
   TradingSignal GenerateSignal();
   
   // 验证信号有效性
   bool ValidateSignal(const TradingSignal &signal);
   
   // 检查入场条件
   ENUM_SIGNAL_TYPE CheckEntryConditions(const N_Structure &structure, int trend_direction);
   
   // 计算信号置信度
   double CalculateSignalConfidence(const TradingSignal &signal, const MarketAnalysisResult &market_analysis);
   
   // 获取信号描述
   string GetSignalDescription(const TradingSignal &signal);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CSignalManager::CSignalManager(string symbol, CMarketAnalyzer* analyzer)
{
   m_symbol = symbol;
   m_analyzer = analyzer;
   m_atr_handle = INVALID_HANDLE;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CSignalManager::~CSignalManager()
{
   if(m_atr_handle != INVALID_HANDLE)
   {
      IndicatorRelease(m_atr_handle);
      m_atr_handle = INVALID_HANDLE;
   }
}

//+------------------------------------------------------------------+
//| 初始化信号管理器                                                   |
//+------------------------------------------------------------------+
bool CSignalManager::Initialize()
{
   if(m_analyzer == NULL)
   {
      Print("错误: 市场分析器为空");
      return false;
   }
   
   if(!InitATRIndicator())
   {
      Print("错误: ATR指标初始化失败");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 初始化ATR指标                                                     |
//+------------------------------------------------------------------+
bool CSignalManager::InitATRIndicator()
{
   TimeFrameConfig tf_config(m_analyzer.GetTimeFrameConfig());
   m_atr_handle = iATR(m_symbol, tf_config.stoploss_timeframe, 14);
   
   if(m_atr_handle == INVALID_HANDLE)
   {
      Print("错误: 无法创建ATR指标句柄");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                        |
//+------------------------------------------------------------------+
double CSignalManager::GetATR(int period, int shift = 0)
{
   if(m_atr_handle == INVALID_HANDLE)
      return 0.0;
   
   double atr_buffer[1];
   if(CopyBuffer(m_atr_handle, 0, shift, 1, atr_buffer) <= 0)
      return 0.0;
   
   return atr_buffer[0];
}

//+------------------------------------------------------------------+
//| 生成交易信号                                                      |
//+------------------------------------------------------------------+
TradingSignal CSignalManager::GenerateSignal()
{
   TradingSignal signal;
   signal.signal_time = TimeCurrent();
   
   // 获取市场分析结果
   MarketAnalysisResult market_analysis(m_analyzer.AnalyzeMarket());
   if(!market_analysis.is_valid)
   {
      signal.description = "市场分析无效";
      return signal;
   }
   
   // 识别N字结构
   N_Structure structure(m_analyzer.IdentifyN_Structure());
   if(!structure.isValid)
   {
      signal.description = "未发现有效N字结构";
      return signal;
   }
   
   signal.structure = structure;
   
   // 检查入场条件
   ENUM_SIGNAL_TYPE entry_signal = CheckEntryConditions(structure, market_analysis.trend_direction);
   if(entry_signal == SIGNAL_NONE)
   {
      signal.description = "入场条件不满足";
      return signal;
   }
   
   signal.signal_type = entry_signal;
   
   // 计算入场价格
   if(entry_signal == SIGNAL_BUY)
      signal.entry_price = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
   else
      signal.entry_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   
   // 计算止损价格
   signal.stop_loss = CalculateStopLoss(structure, entry_signal);
   if(signal.stop_loss <= 0.0)
   {
      signal.description = "无法计算止损价格";
      return signal;
   }
   
   // 计算止盈价格
   signal.take_profit = CalculateTakeProfit(signal.entry_price, signal.stop_loss, 2.0);
   
   // 计算信号置信度
   signal.confidence = CalculateSignalConfidence(signal, market_analysis);
   
   // 确定信号强度
   signal.strength = DetermineSignalStrength(signal);
   
   // 验证信号
   signal.is_valid = ValidateSignal(signal);
   
   // 生成信号描述
   signal.description = GetSignalDescription(signal);
   
   return signal;
}

//+------------------------------------------------------------------+
//| 检查入场条件                                                      |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE CSignalManager::CheckEntryConditions(const N_Structure &structure, int trend_direction)
{
   if(!structure.isValid)
      return SIGNAL_NONE;
   
   // 确保当前时间在C点之后
   if(TimeCurrent() <= structure.timeC)
      return SIGNAL_NONE;
   
   TimeFrameConfig tf_config = m_analyzer.GetTimeFrameConfig();
   
   if(trend_direction == 1) // 多头趋势
   {
      // 检查看涨吞没形态
      if(CheckBullishEngulfing(tf_config.base_timeframe, 1))
         return SIGNAL_BUY;
      
      // 检查价格突破C点
      MqlRates rates[1];
      if(CopyRates(m_symbol, tf_config.base_timeframe, 0, 1, rates) == 1)
      {
         if(rates[0].close > structure.priceC && rates[0].high > structure.priceC)
            return SIGNAL_BUY;
      }
   }
   else if(trend_direction == -1) // 空头趋势
   {
      // 检查看跌吞没形态
      if(CheckBearishEngulfing(tf_config.base_timeframe, 1))
         return SIGNAL_SELL;
      
      // 检查价格跌破C点
      MqlRates rates[1];
      if(CopyRates(m_symbol, tf_config.base_timeframe, 0, 1, rates) == 1)
      {
         if(rates[0].close < structure.priceC && rates[0].low < structure.priceC)
            return SIGNAL_SELL;
      }
   }
   
   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| 检查看涨吞没形态                                                   |
//+------------------------------------------------------------------+
bool CSignalManager::CheckBullishEngulfing(ENUM_TIMEFRAMES timeframe, int shift = 1)
{
   MqlRates rates[2];
   if(CopyRates(m_symbol, timeframe, shift, 2, rates) != 2)
      return false;
   
   return (rates[0].close < rates[0].open &&     // 前一根为阴线
           rates[1].close > rates[1].open &&     // 当前为阳线
           rates[1].open < rates[0].close &&     // 当前开盘低于前一收盘
           rates[1].close > rates[0].open);      // 当前收盘高于前一开盘
}

//+------------------------------------------------------------------+
//| 检查看跌吞没形态                                                   |
//+------------------------------------------------------------------+
bool CSignalManager::CheckBearishEngulfing(ENUM_TIMEFRAMES timeframe, int shift = 1)
{
   MqlRates rates[2];
   if(CopyRates(m_symbol, timeframe, shift, 2, rates) != 2)
      return false;
   
   return (rates[0].close > rates[0].open &&     // 前一根为阳线
           rates[1].close < rates[1].open &&     // 当前为阴线
           rates[1].open > rates[0].close &&     // 当前开盘高于前一收盘
           rates[1].close < rates[0].open);      // 当前收盘低于前一开盘
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                      |
//+------------------------------------------------------------------+
double CSignalManager::CalculateStopLoss(const N_Structure &structure, ENUM_SIGNAL_TYPE signal_type)
{
   double atr_value = GetATR(14, 0);
   if(atr_value <= 0.0)
      atr_value = SymbolInfoDouble(m_symbol, SYMBOL_POINT) * 100;
   
   double atr_buffer = atr_value * 0.5; // ATR缓冲
   
   if(signal_type == SIGNAL_BUY)
   {
      // 多头止损设在C点下方
      return structure.priceC - atr_buffer;
   }
   else if(signal_type == SIGNAL_SELL)
   {
      // 空头止损设在C点上方
      return structure.priceC + atr_buffer;
   }
   
   return 0.0;
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                      |
//+------------------------------------------------------------------+
double CSignalManager::CalculateTakeProfit(double entry_price, double stop_loss, double risk_reward_ratio = 2.0)
{
   if(entry_price <= 0.0 || stop_loss <= 0.0)
      return 0.0;
   
   double risk_points = MathAbs(entry_price - stop_loss);
   double reward_points = risk_points * risk_reward_ratio;
   
   if(entry_price > stop_loss) // 多头
      return entry_price + reward_points;
   else // 空头
      return entry_price - reward_points;
}

//+------------------------------------------------------------------+
//| 计算信号置信度                                                     |
//+------------------------------------------------------------------+
double CSignalManager::CalculateSignalConfidence(const TradingSignal &signal, const MarketAnalysisResult &market_analysis)
{
   double confidence = 0.0;
   
   // 趋势强度贡献 (40%)
   confidence += market_analysis.trend_strength * 0.4;
   
   // N字结构强度贡献 (30%)
   confidence += signal.structure.strength * 0.3;
   
   // 价格位置评估 (20%)
   double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   if(market_analysis.resistance_level > market_analysis.support_level)
   {
      double price_position = (current_price - market_analysis.support_level) / 
                             (market_analysis.resistance_level - market_analysis.support_level);
      
      if(signal.signal_type == SIGNAL_BUY && price_position < 0.3) // 接近支撑位买入
         confidence += 0.2;
      else if(signal.signal_type == SIGNAL_SELL && price_position > 0.7) // 接近阻力位卖出
         confidence += 0.2;
      else
         confidence += 0.1;
   }
   
   // 风险回报比评估 (10%)
   if(signal.take_profit > 0 && signal.stop_loss > 0 && signal.entry_price > 0)
   {
      double risk = MathAbs(signal.entry_price - signal.stop_loss);
      double reward = MathAbs(signal.take_profit - signal.entry_price);
      
      if(risk > 0)
      {
         double rr_ratio = reward / risk;
         if(rr_ratio >= 2.0)
            confidence += 0.1;
         else if(rr_ratio >= 1.5)
            confidence += 0.05;
      }
   }
   
   return MathMin(confidence, 1.0);
}

//+------------------------------------------------------------------+
//| 确定信号强度                                                      |
//+------------------------------------------------------------------+
ENUM_SIGNAL_STRENGTH CSignalManager::DetermineSignalStrength(const TradingSignal &signal)
{
   if(signal.confidence >= 0.8)
      return SIGNAL_STRONG;
   else if(signal.confidence >= 0.6)
      return SIGNAL_MEDIUM;
   else
      return SIGNAL_WEAK;
}

//+------------------------------------------------------------------+
//| 验证信号有效性                                                     |
//+------------------------------------------------------------------+
bool CSignalManager::ValidateSignal(const TradingSignal &signal)
{
   // 基本验证
   if(signal.signal_type == SIGNAL_NONE)
      return false;
   
   if(signal.entry_price <= 0.0 || signal.stop_loss <= 0.0)
      return false;
   
   // 止损距离合理性验证
   double stop_distance = MathAbs(signal.entry_price - signal.stop_loss);
   double min_distance = SymbolInfoInteger(m_symbol, SYMBOL_SPREAD) * 
                        SymbolInfoDouble(m_symbol, SYMBOL_POINT) * 2.0;
   
   if(stop_distance < min_distance)
      return false;
   
   // 置信度阈值验证
   if(signal.confidence < 0.3)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取信号描述                                                      |
//+------------------------------------------------------------------+
string CSignalManager::GetSignalDescription(const TradingSignal &signal)
{
   if(!signal.is_valid)
      return "无效信号";
   
   string strength_text = "";
   switch(signal.strength)
   {
      case SIGNAL_WEAK:   strength_text = "弱"; break;
      case SIGNAL_MEDIUM: strength_text = "中"; break;
      case SIGNAL_STRONG: strength_text = "强"; break;
   }
   
   string signal_text = (signal.signal_type == SIGNAL_BUY) ? "买入" : "卖出";
   
   return StringFormat("%s%s信号 | 置信度:%.1f%% | 入场:%.5f | 止损:%.5f | 止盈:%.5f",
                      strength_text, signal_text, 
                      signal.confidence * 100.0,
                      signal.entry_price, 
                      signal.stop_loss, 
                      signal.take_profit);
}